---
type: "manual"
---

# JavaWeb 项目规则提示词

## 文档规范
- [ ] 添加产品需求文档链接  
- [ ] 补充API接口文档地址  
- [ ] 注明数据库设计文档位置  
- [ ] 检查依赖版本是否精确锁定  

## 项目结构
- [ ] 确认包结构符合分层规范  
- [ ] 检查配置类存放位置  
- [ ] 验证DTO/VO分离情况  
- [ ] 审核异常处理目录结构  

## 代码风格
- [ ] 类/方法命名是否符合约定  
- [ ] 常量命名是否全大写  
- [ ] 接口注释是否完整  
- [ ] 方法参数校验是否齐全  

## 技术实现
- [ ] 确认API响应统一封装  
- [ ] 检查数据库连接池配置  
- [ ] 验证事务注解使用规范  
- [ ] 审核SQL防注入措施  

## 安全合规
- [ ] 参数校验机制是否健全  
- [ ] 敏感数据是否脱敏处理  
- [ ] 权限注解是否正确使用  
- [ ] 错误信息是否避免泄露  

## 日志跟踪
- [ ] 关键操作是否记录日志  
- [ ] 异常日志是否包含上下文  
- [ ] 日志级别使用是否合理  
- [ ] 敏感信息是否过滤  

## 测试覆盖
- [ ] 单元测试是否分层编写  
- [ ] 测试用例是否包含边界值  
- [ ] 集成测试是否模拟真实环境  
- [ ] 性能测试是否达标  

## 特殊要求
- [ ] 标注模块专属约束条件  
- [ ] 检查加密算法合规性  
- [ ] 验证第三方接口降级方案  
- [ ] 审核高并发场景处理逻辑  

## 禁止项
- [ ] 避免硬编码配置值  
- [ ] 禁止直接打印异常堆栈  
- [ ] 不允许出现魔法数值  
- [ ] 禁用非线程安全工具类